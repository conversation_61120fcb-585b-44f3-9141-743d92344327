{"overall_metrics": {"accuracy": 71.15076923076923, "macro_f1": 70.99275501330737, "kappa": 0.6999679999999999}, "model_complexity": {"macs": "100.577M", "parameters": "385.932K", "macs_raw": 100577056.0, "params_raw": 385932.0}, "inference_performance": {"avg_inference_time_ms": 0.04093700188856859, "std_inference_time_ms": 0.0273589179078423, "min_inference_time_ms": 0.018130987882614136, "max_inference_time_ms": 1.3077333569526672}, "dataset_info": {"total_samples": 260000, "dataset_type": "hisar", "input_shape": [2, 1024], "num_classes": 26, "snr_range": [-20.0, 18.0]}, "test_info": {"model_path": "./saved_models/awn/hisar_20250817_164140/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-08-22 10:40:26"}}