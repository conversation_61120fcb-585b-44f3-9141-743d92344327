dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 33000
inference_performance:
  avg_inference_time_ms: 0.018836563283746895
  max_inference_time_ms: 0.4317164421081543
  min_inference_time_ms: 0.017035752534866333
  std_inference_time_ms: 0.020149402552970434
model_complexity:
  macs: 8.159M
  macs_raw: 8159104.0
  parameters: 129.145K
  params_raw: 129145.0
overall_metrics:
  accuracy: 62.18484848484849
  kappa: 0.5840333333333334
  macro_f1: 64.83174051232622
test_info:
  config_path: config.yaml
  model_path: ./saved_models/awn/rml_20250817_124939/models/best_model.pth
  test_date: '2025-08-17 14:54:17'
