dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 33000
inference_performance:
  avg_inference_time_ms: 0.010143771316065933
  max_inference_time_ms: 0.2021170579470121
  min_inference_time_ms: 0.008312985301017761
  std_inference_time_ms: 0.015361048095128406
model_complexity:
  macs: 8.159M
  macs_raw: 8159104.0
  parameters: 129.145K
  params_raw: 129145.0
overall_metrics:
  accuracy: 62.18484848484849
  kappa: 0.5840333333333334
  macro_f1: 64.83174051232622
test_info:
  config_path: config.yaml
  model_path: ./saved_models/awn/rml_20250817_124939/models/best_model.pth
  test_date: '2025-08-22 10:43:19'
